package usecase

import (
	"assetfindr/internal/app/asset/constants"
	"assetfindr/internal/app/asset/dtos"
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/app/asset/repository"
	notifConstants "assetfindr/internal/app/notification/constants"
	notificationDtos "assetfindr/internal/app/notification/dtos"
	notificationUsecase "assetfindr/internal/app/notification/usecase"
	storageUsecase "assetfindr/internal/app/storage/usecase"
	taskConstants "assetfindr/internal/app/task/constants"
	taskModel "assetfindr/internal/app/task/models"
	taskRepo "assetfindr/internal/app/task/repository"
	uploadConstants "assetfindr/internal/app/upload/constants"
	uploadDto "assetfindr/internal/app/upload/dtos"
	uploadModel "assetfindr/internal/app/upload/models"
	uploadRepo "assetfindr/internal/app/upload/repository"
	userIdentityModel "assetfindr/internal/app/user-identity/models"
	userIdentityModels "assetfindr/internal/app/user-identity/models"
	userIdentityRepo "assetfindr/internal/app/user-identity/repository"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/pkg/common/commonlogger"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers"
	"assetfindr/pkg/common/helpers/authhelpers"
	"assetfindr/pkg/common/helpers/calculationhelpers"
	"assetfindr/pkg/common/helpers/contexthelpers"
	"assetfindr/pkg/common/helpers/timehelpers"
	"assetfindr/pkg/common/helpers/tmplhelpers"
	"assetfindr/pkg/common/helpers/tyrecalculationutils"
	"bufio"
	"context"
	"errors"
	"fmt"
	"html/template"
	"io"
	"time"

	"github.com/gocarina/gocsv"
	"github.com/jackc/pgtype"
	"gopkg.in/guregu/null.v4"
)

type AssetLinkedUseCase struct {
	DB                        database.DBUsecase
	AssetLinkedRepository     repository.AssetLinkedRepository
	AssetAssignmentUseCase    *AssetAssignmentUseCase
	AssetAssignmentRepo       repository.AssetAssignmentRepository
	AssetTyreUseCase          *AssetTyreUseCase
	AssetUseCase              *AssetUseCase
	AssetLogRepo              repository.AssetLogRepository
	AssetVehicleRepository    repository.AssetVehicleRepository
	AssetTyreRepository       repository.AssetTyreRepository
	AssetRepository           repository.AssetRepository
	ticketRepo                taskRepo.TicketRepository
	assetStatusRequestUseCase AssetStatusRequestUseCase

	UserIdentityRepo    userIdentityRepo.UserRepository
	assetVehicleUsecase *AssetVehicleUseCase
	storageUseCase      *storageUsecase.StorageUseCase
	uploadRepo          uploadRepo.UploadRepository
	notifUseCase        *notificationUsecase.NotificationUseCase
}

type CreateAssetLinkedResponse struct {
	AssetLinked                 *models.AssetLinked
	AssetLinkedAssetVehicleTyre *models.AssetLinkedAssetVehicleTyre
	Error                       error
	State                       *models.AssetVehicleMeterState `json:"state"`
}

func NewAssetLinkedUseCase(
	DB database.DBUsecase,
	assetLinkedRepository repository.AssetLinkedRepository,
	assetLogRepo repository.AssetLogRepository,
	assetVehicleRepository repository.AssetVehicleRepository,
	assetTyreRepository repository.AssetTyreRepository,
	assetRepository repository.AssetRepository,
	assetAssignmentRepo repository.AssetAssignmentRepository,
	userIdentityRepo userIdentityRepo.UserRepository,
	assetVehicleUsecase *AssetVehicleUseCase,
	storageUseCase *storageUsecase.StorageUseCase,
	uploadRepo uploadRepo.UploadRepository,
	ticketRepo taskRepo.TicketRepository,
	assetStatusRequestUseCase AssetStatusRequestUseCase,
) AssetLinkedUseCase {
	return AssetLinkedUseCase{
		DB:                        DB,
		AssetLinkedRepository:     assetLinkedRepository,
		AssetLogRepo:              assetLogRepo,
		AssetVehicleRepository:    assetVehicleRepository,
		AssetTyreRepository:       assetTyreRepository,
		AssetRepository:           assetRepository,
		AssetAssignmentRepo:       assetAssignmentRepo,
		UserIdentityRepo:          userIdentityRepo,
		assetVehicleUsecase:       assetVehicleUsecase,
		storageUseCase:            storageUseCase,
		uploadRepo:                uploadRepo,
		ticketRepo:                ticketRepo,
		assetStatusRequestUseCase: assetStatusRequestUseCase,
	}
}

func (uc *AssetLinkedUseCase) SetNotifUseCase(notifUseCase *notificationUsecase.NotificationUseCase) {
	uc.notifUseCase = notifUseCase
}

func (uc *AssetLinkedUseCase) UpdateAssetAssignmentUseCase(assetAssignmentUseCase *AssetAssignmentUseCase) {
	uc.AssetAssignmentUseCase = assetAssignmentUseCase
}

func (uc *AssetLinkedUseCase) UpdateAssetTyreUseCase(assetTyreUseCase *AssetTyreUseCase) {
	uc.AssetTyreUseCase = assetTyreUseCase
}

func (uc *AssetLinkedUseCase) UpdateAssetUseCase(assetUseCase *AssetUseCase) {
	uc.AssetUseCase = assetUseCase
}

func (uc *AssetLinkedUseCase) GetAssetLinkedTyres(ctx context.Context, parentAssetId string) (commonmodel.ListResponse, error) {
	assetLinkedResponses := commonmodel.ListResponse{}

	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return assetLinkedResponses, err
	}

	assetsLinked, err := uc.AssetLinkedRepository.GetAssetLinkedTyresV2(
		ctx,
		uc.DB.DB(),
		models.AssetLinkedVehicleTyreCondition{
			Where: models.AssetLinkedVehicleTyreWhere{
				ClientID:      claim.GetLoggedInClientID(),
				ParentAssetID: parentAssetId,
			},
			Preload: models.AssetLinkedVehicleTyrePreload{
				AssetLinked:                true,
				AssetLinkedChildAsset:      true,
				AssetLinkedParentAsset:     true,
				AssetLinkedLinkedAssetType: true,
			},
		},
	)
	if err != nil {
		return assetLinkedResponses, err
	}

	if len(assetsLinked) == 0 {
		return assetLinkedResponses, nil
	}

	mapsUser, err := uc.mapsUser(ctx, assetsLinked)
	if err != nil {
		return assetLinkedResponses, err
	}

	newResponse := dtos.BuildAssetLinkedResponse(assetsLinked, mapsUser)

	assetLinkedResponses = commonmodel.ListResponse{
		TotalRecords: len(newResponse),
		PageSize:     len(newResponse),
		PageNo:       1,
		Data:         newResponse,
	}

	return assetLinkedResponses, nil
}

var ErrAssetLinkedNotFound = errors.New("AssetLinked not found")

func (uc *AssetLinkedUseCase) CreateAssetLinked(ctx context.Context, createDTO *dtos.AssetLinkedResponse) (*commonmodel.UpdateResponse, error) {
	createResponse := &CreateAssetLinkedResponse{}

	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	createDTO.ClientID = claim.GetLoggedInClientID()

	var scenario string
	// link : 1, unlink : 2, replace : 3, nonotif : 0

	exist, _ := uc.AssetLinkedRepository.GetAssetLinkedByID(ctx, uc.DB.DB(), createDTO.UnlinkChildAssetId)

	if createDTO.ParentAssetID == "" {
		createDTO.ParentAssetID = exist.ParentAssetID
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tx.Rollback()

	latestVehicleState, err := uc.AssetLinkedRepository.GetLatestAssetVehicleMeterState(ctx, tx.DB(), models.AssetVehicleMeterStateCondition{
		Where: models.AssetVehicleMeterStateWhere{
			AssetID:  createDTO.ParentAssetID,
			ClientID: createDTO.ClientID,
		},
	})
	if err != nil && !errorhandler.IsErrNotFound(err) {
		return nil, err
	}

	if latestVehicleState != nil {
		createResponse.State = latestVehicleState
	}

	if !createDTO.Datetime.IsZero() {
		vehicleState, err := uc.AssetLinkedRepository.GetAssetVehicleMeterState(ctx, tx.DB(), models.AssetVehicleMeterStateCondition{
			Where: models.AssetVehicleMeterStateWhere{
				AssetID:   createDTO.ParentAssetID,
				DateTime:  createDTO.Datetime,
				VehicleKM: null.IntFrom(int64(createDTO.VehicleKM)),
				VehicleHm: null.IntFrom(int64(calculationhelpers.Multiply100(createDTO.VehicleHm))),
			},
		})
		if err != nil && !errorhandler.IsErrNotFound(err) {
			return nil, err
		}

		if vehicleState == nil {
			assetVehicle, err := uc.AssetVehicleRepository.GetAssetVehicle(ctx, tx.DB(), models.AssetVehicleCondition{
				Where: models.AssetVehicleWhere{
					AssetID: createDTO.ParentAssetID,
				},
			})
			if err != nil {
				return nil, err
			}

			vehicleState = &models.AssetVehicleMeterState{
				AssetID:           createDTO.ParentAssetID,
				DateTime:          createDTO.Datetime,
				VehicleKM:         createDTO.VehicleKM,
				VehicleHm:         calculationhelpers.Multiply100(createDTO.VehicleHm),
				AxleConfiguration: assetVehicle.AxleConfiguration,
			}
			err = uc.AssetLinkedRepository.CreateAssetVehicleMeterState(ctx, tx.DB(), vehicleState)
			if err != nil {
				return nil, err
			}
		}

		createResponse.State = vehicleState
	}

	if createDTO.UnlinkChildAssetId != "" {
		err := uc.processUnlinkAssetTyre(ctx, tx, createResponse, createDTO)
		if err != nil {
			return nil, err
		}
		scenario = constants.ASSET_LINKED_STATUS_UNLINK
	}

	if createDTO.ChildAssetID != "" {
		err = uc.processLinkAssetTyre(ctx, tx, createResponse, createDTO)
		if err != nil {
			return nil, err
		}

		scenario = constants.ASSET_LINKED_STATUS_LINK
	}

	if (createDTO.UnlinkChildAssetId != "" || createDTO.ChildAssetID != "") && createDTO.ParentAssetID != "" {
		err = uc.calculateLinkedTyreRTDMismatch(ctx, createDTO.ParentAssetID, tx)
		if err != nil {
			return nil, err
		}
	}

	if createDTO.ChildAssetID != "" && exist != nil {
		scenario = constants.ASSET_LINKED_STATUS_REPLACE

	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	if constants.MapAssetLinkedStatus()[scenario] != "" {
		go uc.notifyAfterLinkTyre(contexthelpers.WithoutCancel(ctx), createResponse.AssetLinked, scenario)
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: createResponse.AssetLinked.ID,
		Data:        nil,
	}, nil
}

func (uc *AssetLinkedUseCase) notifyAfterLinkTyre(
	ctx context.Context,
	assetLinked *models.AssetLinked,
	scenario string,
) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return
	}
	assetPlatNo := ""
	asset, err := uc.AssetUseCase.GetAssetByID(ctx, assetLinked.ParentAssetID)
	if err != nil {
		commonlogger.Warnf("Error in getting asset by asset id from asset service", err)
		return
	}
	assetName := asset.Name
	if asset.ReferenceNumber == "" {
		assetPlatNo = asset.SerialNumber
	} else {
		assetPlatNo = asset.ReferenceNumber
	}

	client, err := uc.UserIdentityRepo.GetClient(ctx, uc.DB.DB(), userIdentityModel.ClientCondition{
		Where: userIdentityModel.ClientWhere{
			ID: asset.ClientID,
		},
	})
	if err != nil {
		commonlogger.Warnf("failed to get client for email %v", err)
		return
	}

	currentAssetAssignment := &models.AssetAssignment{}
	err = uc.AssetAssignmentUseCase.GetAssetAssignmentByAssetId(ctx, currentAssetAssignment, asset.ID)
	if err != nil {
		commonlogger.Warnf("failed to get current asset assignment %v", err)
		return
	}

	tyrePosition, err := uc.AssetLinkedRepository.GetAssetTyrePositionById(ctx, uc.DB.DB(), assetLinked.ChildAssetID)
	if err != nil {
		commonlogger.Warnf("failed to get asset vehicle tyre %v", err)
		return
	}

	assettHref := uc.notifUseCase.GenerateTargetURL(ctx, client.ClientAlias, notifConstants.DESTINATION_TYPE_ASSET, assetLinked.ParentAssetID)
	loc, _ := time.LoadLocation("Asia/Jakarta")

	templateBod := tmplhelpers.LinkedTyreBod{
		AssetName:         assetName,
		AssetAssignPlatNo: assetPlatNo,
		AssignedBy:        claim.GetName(),
		TyrePosition:      "-",
		DateOfLink:        assetLinked.LinkedDatetime.In(loc).Format(timehelpers.RFC1123Notif),
		RedirectAssetLink: template.URL(assettHref),
		Scenario:          scenario,
	}
	if tyrePosition != 0 {
		templateBod.TyrePosition = fmt.Sprintf("%d", tyrePosition)
	}

	notifItem := notificationDtos.CreateNotificationItem{
		UserID:            currentAssetAssignment.UserID,
		SourceCode:        notifConstants.NOTIFICATION_SOURCE_CODE_TICKET_UPDATE,
		SourceReferenceID: assetLinked.ParentAssetID,
		TargetReferenceID: assetLinked.ParentAssetID,
		TargetURL:         assettHref,
		MessageHeader:     templateBod.ConstructSubjectEmail(),
		MessageBody:       templateBod.ConstructBodyEmail(),
		MessageFirebase: notificationDtos.MessageFirebase{
			Title: templateBod.ConstrucPushNotifSubject(),
			Body:  templateBod.ConstrucPushNotifBody(),
		},
		ClientID:        claim.GetLoggedInClientID(),
		TypeCode:        "",
		ContentTypeCode: "",
		ReferenceCode:   notifConstants.NOTIF_REF_ASSET,
		ReferenceValue:  assetLinked.ID,
	}

	_ = uc.notifUseCase.CreateNotification(ctx, notificationDtos.CreateNotificationReq{
		Items:           []notificationDtos.CreateNotificationItem{notifItem},
		SendToEmail:     true,
		SendToPushNotif: true,
	})
}

func (uc *AssetLinkedUseCase) processLinkAssetTyreWrapper(ctx context.Context, createResponse *CreateAssetLinkedResponse, createDTO *dtos.AssetLinkedResponse) error {

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return err
	}

	defer tx.Rollback()

	err = uc.processLinkAssetTyre(ctx, tx, createResponse, createDTO)
	if err != nil {
		return err
	}

	err = tx.Commit()
	if err != nil {
		return err
	}

	return nil
}

func (uc *AssetLinkedUseCase) processLinkAssetTyre(ctx context.Context, tx database.DBUsecase, createResponse *CreateAssetLinkedResponse, createDTO *dtos.AssetLinkedResponse) error {

	childAsset, err := uc.AssetTyreRepository.GetAssetTyre(ctx, tx.DB(), models.AssetTyreCondition{
		Where: models.AssetTyreWhere{
			AssetID: createDTO.ChildAssetID,
		},
		Preload: models.AssetTyrePreload{
			Asset: true,
		},
	})
	if err != nil {
		return err
	}

	assetVehicle, err := uc.AssetVehicleRepository.GetAssetVehicle(ctx, tx.DB(), models.AssetVehicleCondition{
		Where: models.AssetVehicleWhere{
			AssetID: createDTO.ParentAssetID,
		},
	})

	if err != nil {
		return err
	}

	_, err = uc.AssetLinkedRepository.GetAssetLinkedTyre(ctx, tx.DB(), models.AssetLinkedVehicleTyreCondition{
		Where: models.AssetLinkedVehicleTyreWhere{
			TyrePosition:  createDTO.TyrePosition,
			ParentAssetID: createDTO.ParentAssetID,
		},
	})
	if err != nil && !errorhandler.IsErrNotFound(err) {
		return err
	}

	if err == nil {
		return errorhandler.ErrBadRequest("current position already filled")
	}

	if (assetVehicle.UseHourmeter.ValueOrZero() && childAsset.UseKM()) ||
		(assetVehicle.UseKilometer.ValueOrZero() && childAsset.UseHM()) {
		return errorhandler.ErrBadRequest("miss match tyre measurement type with vehicle")
	}

	// TODO: Need to validate is child already linked to assete

	linkedDatetime := time.Now().In(time.UTC)
	if createResponse.State != nil {
		linkedDatetime = createResponse.State.DateTime
	}

	assetLinked := &models.AssetLinked{
		ChildAssetID:        createDTO.ChildAssetID,
		ParentAssetID:       createDTO.ParentAssetID,
		LinkedDatetime:      linkedDatetime,
		UnlinkedDatetime:    nil,
		ClientID:            createDTO.ClientID,
		LinkedAssetTypeCode: createDTO.LinkedAssetTypeCode,
	}

	if createResponse.State != nil {
		assetLinked.LinkedStateID = createResponse.State.ID
	}

	assetLinked.SetUUID("las")

	assetLinkedAssetVehicleTyre := &models.AssetLinkedAssetVehicleTyre{
		AssetLinkedID:     assetLinked.ID,
		TyrePosition:      createDTO.TyrePosition,
		OnLinkedVehicleKm: int(assetVehicle.VehicleKM),
		OnLinkedTyreKm:    childAsset.TotalKM,
		OnLinkedVehicleHm: assetVehicle.VehicleHm,
		OnLinkedTyreHm:    childAsset.TotalHm,
		// OnUnlinkedVehicleKm: createDTO.OnUnlinkedVehicleKm,
		// OnUnlinkedTyreKm:    createDTO.OnUnlinkedTyreKm,
		ClientID:      createDTO.ClientID,
		RetreadNumber: childAsset.RetreadNumber,
	}

	if !createDTO.Datetime.IsZero() {
		assetLinked.LinkedDatetime = createDTO.Datetime
		assetLinkedAssetVehicleTyre.OnLinkedVehicleKm = createDTO.VehicleKM
		assetLinkedAssetVehicleTyre.OnLinkedVehicleHm = calculationhelpers.Multiply100(createDTO.VehicleHm)
	}

	if err := uc.AssetLinkedRepository.CreateAssetLinked(ctx, tx.DB(), assetLinked); err != nil {
		return err
	}

	assetLinkedAssetVehicleTyre.AssetLinkedID = assetLinked.ID

	if createDTO.Datetime.IsZero() ||
		(childAsset.Asset.AssetStatusCode == constants.ASSET_STATUS_CODE_IN_STOCK ||
			childAsset.Asset.AssetStatusCode == constants.ASSET_STATUS_CODE_NEW_STOCK) {

		if err := uc.AssetRepository.UpdateAssetStatusCode(ctx, tx.DB(), createDTO.ChildAssetID, constants.ASSET_STATUS_CODE_INSTALLED); err != nil {
			return err
		}
	}

	measurementUnitCode := constants.TYRE_METER_CALCULATION_CODE_KM
	if assetVehicle.UseHourmeter.ValueOrZero() {
		measurementUnitCode = constants.TYRE_METER_CALCULATION_CODE_HM
	}

	updateAssetTyre := &models.AssetTyre{
		MeterCalculationCode: null.StringFrom(measurementUnitCode),
	}

	if !childAsset.FirstInstalledDatetime.Valid {
		updateAssetTyre.FirstInstalledDatetime = null.TimeFrom(assetLinked.LinkedDatetime)
	}

	if createDTO.TyrePosition > int(assetVehicle.NumberOfTyres.Int64) {
		isSpare := null.BoolFrom(true)
		updateAssetTyre.IsSpare = &isSpare
		assetLinkedAssetVehicleTyre.IsSpare = null.BoolFrom(true)
	}

	updateAssetTyre.AssetID = createDTO.ChildAssetID
	err = uc.AssetTyreRepository.UpdateAssetTyre(ctx, tx.DB(), updateAssetTyre)
	if err != nil {
		return err
	}

	if !createDTO.Datetime.IsZero() && createDTO.TyrePosition <= int(assetVehicle.NumberOfTyres.Int64) {
		increasedKmToCurrent := int(assetVehicle.VehicleKM) - createDTO.VehicleKM
		increasedHmToCurrent := assetVehicle.VehicleHm - calculationhelpers.Multiply100(createDTO.VehicleHm)
		err = uc.AssetTyreRepository.IncreaseAssetTyreMeters(ctx, tx.DB(), createDTO.ChildAssetID, increasedKmToCurrent, increasedHmToCurrent, 0)
		if err != nil {
			return err
		}
	}

	if err := uc.AssetLinkedRepository.CreateAssetLinkedAssetVehiceTyre(ctx, tx.DB(), assetLinkedAssetVehicleTyre); err != nil {
		return err
	}

	createResponse.AssetLinked = assetLinked
	createResponse.AssetLinkedAssetVehicleTyre = assetLinkedAssetVehicleTyre

	return nil
}

func (uc *AssetLinkedUseCase) processUnlinkAssetTyre(ctx context.Context, tx database.DBUsecase, createResponse *CreateAssetLinkedResponse, createDTO *dtos.AssetLinkedResponse) error {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return err
	}

	assetLinked, err := uc.AssetLinkedRepository.GetAssetLinkedByID(ctx, tx.DB(), createDTO.UnlinkChildAssetId)
	if err != nil {
		return err
	}
	assetLinkedAssetVehicleTyre, err := uc.AssetLinkedRepository.GetAssetLinkedAssetVehiceTyreByID(ctx, tx.DB(), createDTO.UnlinkChildAssetId)
	if err != nil {
		return err
	}

	// Condition on spare tyre
	assetLinkedAssetVehicleTyre.OnUnlinkedTyreKm = assetLinkedAssetVehicleTyre.OnLinkedTyreKm
	assetLinkedAssetVehicleTyre.OnUnlinkedVehicleKm = assetLinkedAssetVehicleTyre.OnLinkedVehicleKm

	assetLinkedAssetVehicleTyre.OnUnlinkedTyreHm = assetLinkedAssetVehicleTyre.OnLinkedTyreHm
	assetLinkedAssetVehicleTyre.OnUnlinkedVehicleHm = assetLinkedAssetVehicleTyre.OnLinkedVehicleHm

	unlinkedDatetime := time.Now().In(time.UTC)

	if createResponse.State != nil {
		assetLinked.UnlinkedStateID = createResponse.State.ID
		unlinkedDatetime = createResponse.State.DateTime
	}

	if !createDTO.Datetime.IsZero() {
		unlinkedDatetime = createDTO.Datetime
	}
	assetLinked.UnlinkedDatetime = &unlinkedDatetime

	if createDTO.Datetime.IsZero() &&
		createDTO.CauseOfUnlink == constants.CAUSE_OF_UNLINK_MOVE_TO_STOCK {
		if err = uc.AssetRepository.UpdateAssetStatusCode(ctx, tx.DB(), assetLinked.ChildAssetID, constants.ASSET_STATUS_CODE_IN_STOCK); err != nil {
			return err
		}

		err = uc.AssetTyreRepository.UpdateAssetTyre(ctx, tx.DB(), &models.AssetTyre{
			AssetID:       assetLinked.ChildAssetID,
			LastStockDate: time.Now().In(time.UTC),
		})
		if err != nil {
			return err
		}
	} else if createDTO.Datetime.IsZero() &&
		createDTO.CauseOfUnlink == constants.CAUSE_OF_UNLINK_TYRE_DEMAGE {
		if createDTO.UnlinkTyreStatus != constants.ASSET_STATUS_CODE_IN_REPAIR &&
			createDTO.UnlinkTyreStatus != constants.ASSET_STATUS_CODE_SCRAP_PENDING {
			return errorhandler.ErrBadRequest("unlink_tyre_status not accepted")
		}

		if createDTO.UnlinkTyreStatus == constants.ASSET_STATUS_CODE_IN_REPAIR {
			if err = uc.AssetRepository.UpdateAssetStatusCode(ctx, tx.DB(), assetLinked.ChildAssetID, createDTO.UnlinkTyreStatus); err != nil {
				return err
			}

			dataInformation, err := uc.AssetRepository.GetAssetDataInformation(ctx, uc.DB.DB(), assetLinked.ChildAssetID)
			if err != nil {
				commonlogger.Errorf("failed to get asset data information, err:%v", err)
				return err
			}

			if createDTO.IsNeedCreateTicket {
				if createDTO.TicketType == taskConstants.TICKET_TYPE_TASK {
					// Create Ticket
					err = uc.ticketRepo.CreateTask(ctx, tx.DB(), &taskModel.Task{
						Description: null.NewString(createDTO.Notes, createDTO.Notes != ""),
						Subject:     createDTO.TicketSubject,
						AssetID:     assetLinked.ChildAssetID,
						StatusCode:  taskConstants.TASK_STATUS_OPEN,
					})
					if err != nil {
						return err
					}
				} else {
					// Create Ticket
					err = uc.ticketRepo.CreateTicket(ctx, tx.DB(), &taskModel.Ticket{
						Description:          createDTO.Notes,
						Subject:              createDTO.TicketSubject,
						TicketCategoryCode:   createDTO.TicketCategoryID,
						TicketReferenceCode:  taskConstants.TICKET_ASSET_REF,
						ReferenceID:          assetLinked.ChildAssetID,
						RequesterUserID:      claim.UserID,
						StatusCode:           taskConstants.TICKET_STATUS_CODE_OPEN,
						SeverityLevelCode:    taskConstants.TICKET_SEVERITY_NOT_SET,
						AssetDataInformation: pgtype.JSONB{Bytes: dataInformation, Status: pgtype.Present},
					})
					if err != nil {
						return err
					}
				}
			}

		} else if createDTO.UnlinkTyreStatus == constants.ASSET_STATUS_CODE_SCRAP_PENDING {
			err := uc.assetStatusRequestUseCase.CreateAssetStatusRequest(ctx, &models.AssetStatusRequest{
				AssetID:       assetLinked.ChildAssetID,
				ReasonCode:    createDTO.UnlinkScrapStatusRequest.ReasonCode,
				Reason:        createDTO.UnlinkScrapStatusRequest.Reason,
				SubReasonCode: createDTO.UnlinkScrapStatusRequest.SubReasonCode,
				SubReason:     createDTO.UnlinkScrapStatusRequest.SubReason,
				Notes:         createDTO.Notes,
				StatusCode:    constants.ASSET_STATUS_REQUEST_PENDING,
				TypeCode:      constants.ASSET_STATUS_REQUEST_TYPE_SCRAP,
				GradeCode:     createDTO.UnlinkScrapStatusRequest.GradeCode,
				GradeReason:   createDTO.UnlinkScrapStatusRequest.GradeReason,
			}, createDTO.UnlinkScrapStatusRequest.Photos)
			if err != nil {
				return err
			}
		}
	}

	if assetLinkedAssetVehicleTyre.TyrePosition <= int(assetLinked.ParentAsset.NumberOfTyres.Int64) {
		updateAssetTyre := &models.AssetTyre{}
		updateAssetTyre.AssetID = assetLinked.ChildAssetID

		assetVehicle, err := uc.AssetVehicleRepository.GetAssetVehicleByID(ctx, tx.DB(), assetLinked.ParentAssetID)
		if err != nil {
			return err
		}

		if !createDTO.Datetime.IsZero() {
			vehicleHM := calculationhelpers.Multiply100(createDTO.VehicleHm)
			increasedKM := createDTO.VehicleKM - assetLinkedAssetVehicleTyre.OnLinkedVehicleKm
			assetLinkedAssetVehicleTyre.OnUnlinkedTyreKm = increasedKM + assetLinkedAssetVehicleTyre.OnLinkedTyreKm
			assetLinkedAssetVehicleTyre.OnUnlinkedVehicleKm = createDTO.VehicleKM
			increasedHM := vehicleHM - assetLinkedAssetVehicleTyre.OnLinkedVehicleHm
			assetLinkedAssetVehicleTyre.OnUnlinkedTyreHm = increasedHM + assetLinkedAssetVehicleTyre.OnLinkedTyreHm
			assetLinkedAssetVehicleTyre.OnUnlinkedVehicleHm = vehicleHM

			decreaseKm := createDTO.VehicleKM - int(assetVehicle.VehicleKM)
			decreaseHm := vehicleHM - assetVehicle.VehicleHm
			increaseLifetime := int(unlinkedDatetime.Sub(assetLinked.LinkedDatetime).Seconds())
			err = uc.AssetTyreRepository.IncreaseAssetTyreMeters(ctx, tx.DB(), assetLinked.ChildAssetID, decreaseKm, decreaseHm, increaseLifetime)
			if err != nil {
				return err
			}

		} else {
			assetLinkedAssetVehicleTyre.OnUnlinkedTyreKm = assetLinked.ChildAsset.TotalKM
			assetLinkedAssetVehicleTyre.OnUnlinkedVehicleKm = int(assetVehicle.VehicleKM)
			assetLinkedAssetVehicleTyre.OnUnlinkedTyreHm = assetLinked.ChildAsset.TotalHm
			assetLinkedAssetVehicleTyre.OnUnlinkedVehicleHm = assetVehicle.VehicleHm
			updateAssetTyre.TotalLifetime = assetLinked.ChildAsset.TotalLifetime + int(unlinkedDatetime.Sub(assetLinked.LinkedDatetime).Seconds())
		}

		err = uc.AssetTyreRepository.UpdateAssetTyre(ctx, tx.DB(), updateAssetTyre)
		if err != nil {
			return err
		}

		if !createDTO.Datetime.IsZero() {
			err := uc.AssetTyreRepository.UpdateAssetTyreStatsHistory(ctx, tx.DB(), assetLinked.ChildAssetID)
			if err != nil {
				return err
			}
		}
	}

	if assetLinkedAssetVehicleTyre.TyrePosition > int(assetLinked.ParentAsset.NumberOfTyres.Int64) {
		updateAssetTyre := &models.AssetTyre{}
		updateAssetTyre.AssetID = assetLinked.ChildAssetID
		updateAssetTyre.IsSpare = &null.Bool{}

		err := uc.AssetTyreRepository.UpdateAssetTyre(ctx, tx.DB(), updateAssetTyre)
		if err != nil {
			return err
		}
	}

	if err := uc.AssetLinkedRepository.UpdateAssetLinkedAssetVehicleTyre(ctx, tx.DB(), assetLinkedAssetVehicleTyre); err != nil {
		return err
	}

	if err := uc.AssetLinkedRepository.UpdateAssetLinkedV2(ctx, tx.DB(), assetLinked.ID, assetLinked); err != nil {
		return err
	}

	createResponse.AssetLinked = assetLinked
	createResponse.AssetLinkedAssetVehicleTyre = assetLinkedAssetVehicleTyre

	if err := uc.AssetAssignmentUseCase.UnAssignAssetAssignmentByAssetId(ctx, assetLinked.ChildAssetID); err != nil {
		return err
	}

	return nil
}

func (uc *AssetLinkedUseCase) GetLinkedAssetMapByChildAssetIds(ctx context.Context, linkedAssetMapByChildAssetIds *map[string]models.AssetLinked, childAssetIds []string) error {
	var assetLinkeds []models.AssetLinked

	err := uc.AssetLinkedRepository.GetAssetLinkedByChildAssetIds(ctx, uc.DB.DB(), &assetLinkeds, childAssetIds)
	if err != nil {
		return err
	}

	// Ensure 'usersMapById' is initialized before storing users
	if *linkedAssetMapByChildAssetIds == nil {
		*linkedAssetMapByChildAssetIds = make(map[string]models.AssetLinked)
	}

	for _, value := range assetLinkeds {
		(*linkedAssetMapByChildAssetIds)[value.ChildAssetID] = value
	}

	return nil
}

// TODO: currently skipped in meter state, use traditional link unlink instead
func (uc *AssetLinkedUseCase) UpdateAssetLinkedTyrePositions(ctx context.Context, req dtos.UpdateAssetLinkedTyrePositionReq) error {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return err
	}

	tX, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return err
	}

	defer tX.Rollback()

	assetLinkedIDs := make([]string, 0, len(req.LinkedAssets))
	for _, linkedAsset := range req.LinkedAssets {
		assetLinkedIDs = append(assetLinkedIDs, linkedAsset.AssetLinkedID)
	}

	// Get asset linkeds
	assetLinkeds, err := uc.AssetLinkedRepository.GetAssetLinkeds(ctx, tX.DB(), models.AssetLinkedCondition{
		Where: models.AssetLinkedWhere{
			IDs:      assetLinkedIDs,
			ClientID: claim.GetLoggedInClientID(),
		},
		Preload: models.AssetLinkedPreload{
			AssetLinkedAssetVehicleTyre: true,
			AssetParent:                 true,
			ChildAsset:                  true,
		},
		IsForUpdate: true,
	})
	if err != nil {
		return err
	}

	mapCurrentLinkedTyrePosition := map[int]string{}
	for _, assetLinked := range assetLinkeds {
		mapCurrentLinkedTyrePosition[assetLinked.AssetLinkedAssetVehicleTyre.TyrePosition] = assetLinked.ID
	}

	if len(assetLinkeds) == 0 {
		return errorhandler.ErrBadRequest("asset linkeds not found")
	}

	if len(assetLinkeds) != len(req.LinkedAssets) {
		return errorhandler.ErrBadRequest("some asset linkeds with requested IDs isn't found")
	}

	meterState, err := uc.AssetLinkedRepository.GetLatestAssetVehicleMeterState(ctx, tX.DB(), models.AssetVehicleMeterStateCondition{
		Where: models.AssetVehicleMeterStateWhere{
			AssetID:  assetLinkeds[0].ParentAssetID,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil && !errorhandler.IsErrNotFound(err) {
		return err
	}

	if !req.Datetime.IsZero() {
		vehicleState, err := uc.AssetLinkedRepository.GetAssetVehicleMeterState(ctx, tX.DB(), models.AssetVehicleMeterStateCondition{
			Where: models.AssetVehicleMeterStateWhere{
				AssetID:   assetLinkeds[0].ParentAssetID,
				DateTime:  req.Datetime,
				VehicleKM: null.IntFrom(int64(req.VehicleKM)),
				VehicleHm: null.IntFrom(int64(calculationhelpers.Multiply100(req.VehicleHm))),
			},
		})
		if err != nil && !errorhandler.IsErrNotFound(err) {
			return err
		}

		if vehicleState == nil {
			assetVehicle, err := uc.AssetVehicleRepository.GetAssetVehicle(ctx, tX.DB(), models.AssetVehicleCondition{
				Where: models.AssetVehicleWhere{
					AssetID: assetLinkeds[0].ParentAssetID,
				},
			})
			if err != nil {
				return err
			}

			vehicleState = &models.AssetVehicleMeterState{
				AssetID:           assetLinkeds[0].ParentAssetID,
				DateTime:          req.Datetime,
				VehicleKM:         req.VehicleKM,
				VehicleHm:         calculationhelpers.Multiply100(req.VehicleHm),
				AxleConfiguration: assetVehicle.AxleConfiguration,
			}
			err = uc.AssetLinkedRepository.CreateAssetVehicleMeterState(ctx, tX.DB(), vehicleState)
			if err != nil {
				return err
			}
		}

		meterState = vehicleState
	}

	// get parent for first asset linked
	parentAssetID := assetLinkeds[0].ParentAssetID

	// validate requested assetLinkedIDs
	for _, assetLinked := range assetLinkeds {
		// asset linked should vehicle tyre
		if assetLinked.AssetLinkedAssetVehicleTyre == nil ||
			// all parent must be the same
			parentAssetID != assetLinked.ParentAssetID {
			return errorhandler.ErrBadRequest("some asset linkeds with requested IDs isn't found")
		}
	}

	// get parent vehicle
	assetVehicle, err := uc.AssetVehicleRepository.GetAssetVehicle(ctx,
		tX.DB(),
		models.AssetVehicleCondition{
			Where: models.AssetVehicleWhere{
				AssetID: assetLinkeds[0].ParentAssetID,
			},
			IsForUpdate: true,
		})
	if err != nil {
		return err
	}

	// generate map for new tyre position
	mapAssetLinkedIncommingTyrePosition := map[string]int{}
	for _, linkedAsset := range req.LinkedAssets {
		mapAssetLinkedIncommingTyrePosition[linkedAsset.AssetLinkedID] = linkedAsset.TyrePosition
	}

	newAssetLinkeds := []models.AssetLinked{}

	datetime := time.Now()
	if meterState != nil {
		datetime = meterState.DateTime
	}

	if !req.Datetime.IsZero() {
		datetime = req.Datetime
	}
	for _, assetLinked := range assetLinkeds {
		// if no change position then do nothing

		incommingPosition := mapAssetLinkedIncommingTyrePosition[assetLinked.ID]
		prevTyrePosition := assetLinked.AssetLinkedAssetVehicleTyre.TyrePosition
		if incommingPosition == prevTyrePosition {
			continue
		}

		if currentLinkedID, ok := mapCurrentLinkedTyrePosition[incommingPosition]; ok {
			if _, ok := mapAssetLinkedIncommingTyrePosition[currentLinkedID]; !ok {
				return errorhandler.ErrBadRequest("tyre position already filled")
			}
		}

		// Calculate vehicle and tyre metrics
		vehicleKm := int(assetVehicle.VehicleKM)
		vehicleHm := assetVehicle.VehicleHm
		tyreKM := assetLinked.ChildAsset.TotalKM
		tyreHm := assetLinked.ChildAsset.TotalHm
		if !req.Datetime.IsZero() {
			vehicleKm = req.VehicleKM
			vehicleHm = calculationhelpers.Multiply100(req.VehicleHm)

			// For spare tyres, use the original linked values
			tyreKM = assetLinked.AssetLinkedAssetVehicleTyre.OnLinkedTyreKm
			tyreHm = assetLinked.AssetLinkedAssetVehicleTyre.OnLinkedTyreHm

			// For active tyres, add the accumulated distance
			if prevTyrePosition <= int(assetVehicle.NumberOfTyres.Int64) {
				tyreKM += vehicleKm - assetLinked.AssetLinkedAssetVehicleTyre.OnLinkedVehicleKm
				tyreHm += vehicleHm - assetLinked.AssetLinkedAssetVehicleTyre.OnLinkedVehicleHm
			}
		}
		incommingIsSpare := null.BoolFrom(false)
		if incommingPosition > int(assetVehicle.NumberOfTyres.Int64) {
			incommingIsSpare = null.BoolFrom(true)
		}

		newAssetLinked := models.AssetLinked{
			ChildAssetID:        assetLinked.ChildAssetID,
			ParentAssetID:       assetLinked.ParentAssetID,
			LinkedDatetime:      datetime,
			ClientID:            claim.GetLoggedInClientID(),
			LinkedAssetTypeCode: constants.ASSET_LINKED_TYPE_VEHICLE_TYRE,
			AssetLinkedAssetVehicleTyre: &models.AssetLinkedAssetVehicleTyre{
				TyrePosition:      incommingPosition,
				OnLinkedVehicleKm: vehicleKm,
				OnLinkedVehicleHm: vehicleHm,
				OnLinkedTyreKm:    tyreKM,
				OnLinkedTyreHm:    tyreHm,
				RetreadNumber:     assetLinked.ChildAsset.RetreadNumber,
				ClientID:          claim.GetLoggedInClientID(),
				IsSpare:           incommingIsSpare,
			},
		}

		if meterState != nil {
			newAssetLinked.LinkedStateID = meterState.ID
		}

		newAssetLinkeds = append(newAssetLinkeds, newAssetLinked)

		updateAssetLinked := &models.AssetLinked{
			ModelV2:          commonmodel.ModelV2{ID: assetLinked.ID},
			UnlinkedDatetime: &datetime,
		}

		if meterState != nil {
			updateAssetLinked.UnlinkedStateID = meterState.ID
		}

		err = uc.AssetLinkedRepository.UpdateAssetLinkedV2(ctx, tX.DB(), assetLinked.ID, updateAssetLinked)
		if err != nil {
			return err
		}

		// if km increased and tyre is not spare then update asset tyre
		if prevTyrePosition <= int(assetVehicle.NumberOfTyres.Int64) {
			updateAssetTyre := &models.AssetTyre{}
			updateAssetTyre.AssetID = assetLinked.ChildAssetID

			if req.Datetime.IsZero() {
				updateAssetTyre.TotalLifetime = assetLinked.ChildAsset.TotalLifetime + int(datetime.Sub(assetLinked.LinkedDatetime).Seconds())
			}

			err := uc.AssetTyreRepository.UpdateAssetTyre(ctx, tX.DB(), updateAssetTyre)
			if err != nil {
				return err
			}

			if !req.Datetime.IsZero() {
				err := uc.AssetTyreRepository.UpdateAssetTyreStatsHistory(ctx, tX.DB(), assetLinked.ChildAssetID)
				if err != nil {
					return err
				}
			}
		}

		if prevTyrePosition > int(assetVehicle.NumberOfTyres.Int64) &&
			incommingPosition <= int(assetVehicle.NumberOfTyres.Int64) {
			updateAssetTyre := &models.AssetTyre{}
			updateAssetTyre.AssetID = assetLinked.ChildAssetID
			isSpare := null.BoolFrom(false)
			updateAssetTyre.IsSpare = &isSpare

			err := uc.AssetTyreRepository.UpdateAssetTyre(ctx, tX.DB(), updateAssetTyre)
			if err != nil {
				return err
			}
		}

		if incommingPosition > int(assetVehicle.NumberOfTyres.Int64) &&
			prevTyrePosition <= int(assetVehicle.NumberOfTyres.Int64) {
			updateAssetTyre := &models.AssetTyre{}
			updateAssetTyre.AssetID = assetLinked.ChildAssetID
			isSpare := null.BoolFrom(true)
			updateAssetTyre.IsSpare = &isSpare

			err := uc.AssetTyreRepository.UpdateAssetTyre(ctx, tX.DB(), updateAssetTyre)
			if err != nil {
				return err
			}
		}

		if !req.Datetime.IsZero() {
			// base scneario from Spare to Spare or Non-Spare to Non-Spare no increase or decrease
			increasedKm := 0
			increasedHm := 0

			// base from spare, no increase
			increasedLifetime := 0

			isFromSpare := prevTyrePosition > int(assetVehicle.NumberOfTyres.Int64)
			isToSpare := incommingPosition > int(assetVehicle.NumberOfTyres.Int64)

			// from Non-Spare to Spare decrease
			if !isFromSpare && isToSpare {
				increasedKm = vehicleKm - int(assetVehicle.VehicleKM)
				increasedHm = vehicleHm - assetVehicle.VehicleHm
			}

			if isFromSpare && !isToSpare {
				increasedKm = int(assetVehicle.VehicleKM) - vehicleKm
				increasedHm = assetVehicle.VehicleHm - vehicleHm
			}

			if !isFromSpare {
				increasedLifetime = int(datetime.Sub(assetLinked.LinkedDatetime).Seconds())
			}

			err = uc.AssetTyreRepository.IncreaseAssetTyreMeters(ctx, tX.DB(), assetLinked.ChildAssetID, increasedKm, increasedHm, increasedLifetime)
			if err != nil {
				return err
			}
		}

		err = uc.AssetLinkedRepository.UpdateAssetLinkedAssetVehicleTyreV2(ctx, tX.DB(),
			&models.AssetLinkedAssetVehicleTyre{
				AssetLinkedID:       assetLinked.ID,
				OnUnlinkedVehicleKm: vehicleKm,
				OnUnlinkedVehicleHm: vehicleHm,
				OnUnlinkedTyreKm:    tyreKM,
				OnUnlinkedTyreHm:    tyreHm,
			})
		if err != nil {
			return err
		}
	}

	if len(newAssetLinkeds) > 0 {
		err = uc.AssetLinkedRepository.CreateAssetLinkeds(ctx, tX.DB(), newAssetLinkeds)
		if err != nil {
			return err
		}
	}

	err = uc.calculateLinkedTyreRTDMismatch(ctx, parentAssetID, tX)
	if err != nil {
		return err
	}

	err = tX.Commit()
	if err != nil {
		return err
	}

	return nil
}

// Helper function to calculate vehicle and tyre metrics
func (uc *AssetLinkedUseCase) calculateVehicleAndTyreMetrics(
	assetLinked models.AssetLinked,
	assetVehicle *models.AssetVehicle,
	req dtos.UpdateAssetLinkedTyrePositionReq,
	currentPosition int,
) (vehicleKm int, vehicleHm int, tyreKM int, tyreHm int) {
	vehicleKm = int(assetVehicle.VehicleKM)
	vehicleHm = assetVehicle.VehicleHm
	tyreKM = assetLinked.ChildAsset.TotalKM
	tyreHm = assetLinked.ChildAsset.TotalHm

	if !req.Datetime.IsZero() {
		vehicleKm = req.VehicleKM
		vehicleHm = calculationhelpers.Multiply100(req.VehicleHm)

		// For spare tyres, use the original linked values
		tyreKM = assetLinked.AssetLinkedAssetVehicleTyre.OnLinkedTyreKm
		tyreHm = assetLinked.AssetLinkedAssetVehicleTyre.OnLinkedTyreHm

		// For active tyres, add the accumulated distance
		if currentPosition <= int(assetVehicle.NumberOfTyres.Int64) {
			tyreKM += vehicleKm - assetLinked.AssetLinkedAssetVehicleTyre.OnLinkedVehicleKm
			tyreHm += vehicleHm - assetLinked.AssetLinkedAssetVehicleTyre.OnLinkedVehicleHm
		}
	}

	return vehicleKm, vehicleHm, tyreKM, tyreHm
}

func (uc *AssetLinkedUseCase) GetAssetLinkedTyreHistory(ctx context.Context, childAssetID string, req dtos.AssetLinkedListReq) (commonmodel.ListResponse, error) {
	resp := commonmodel.ListResponse{
		PageSize: req.PageSize,
		PageNo:   req.PageNo,
	}

	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return resp, err
	}

	count, assetsLinked, err := uc.AssetLinkedRepository.GetAssetLinkedTyreHistory(
		ctx,
		uc.DB.DB(),
		models.GetAssetLinkedListParam{
			ListRequest: req.ListRequest,
			Cond: models.AssetLinkedVehicleTyreCondition{
				Where: models.AssetLinkedVehicleTyreWhere{
					ClientID:     claim.GetLoggedInClientID(),
					ChildAssetID: childAssetID,
				},
				Preload: models.AssetLinkedVehicleTyrePreload{
					AssetLinked:                true,
					AssetLinkedChildAsset:      true,
					AssetLinkedParentAsset:     true,
					AssetLinkedAssetParent:     true,
					AssetLinkedLinkedAssetType: true,
				},
			},
		})
	if err != nil {
		return resp, err
	}

	if len(assetsLinked) == 0 {
		return resp, nil
	}

	mapsUser, err := uc.mapsUser(ctx, assetsLinked)
	if err != nil {
		return resp, err
	}

	newResponse := dtos.BuildAssetLinkedResponse(assetsLinked, mapsUser)

	resp.Data = newResponse
	resp.TotalRecords = count

	return resp, nil
}

func (uc *AssetLinkedUseCase) GetAssetLinkedVehicleHistory(ctx context.Context, parentAssetID string, req dtos.AssetLinkedListReq) (commonmodel.ListResponse, error) {
	resp := commonmodel.ListResponse{
		PageSize: req.PageSize,
		PageNo:   req.PageNo,
	}

	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return resp, err
	}

	count, assetsLinked, err := uc.AssetLinkedRepository.GetAssetLinkedVehicleHistory(
		ctx,
		uc.DB.DB(),
		models.GetAssetLinkedListParam{
			ListRequest: req.ListRequest,
			Cond: models.AssetLinkedVehicleTyreCondition{
				Where: models.AssetLinkedVehicleTyreWhere{
					ClientID:      claim.GetLoggedInClientID(),
					ParentAssetID: parentAssetID,
				},
			},
		})
	if err != nil {
		return resp, err
	}

	if len(assetsLinked) == 0 {
		return resp, nil
	}

	mapsUser, err := uc.mapsUser(ctx, assetsLinked)
	if err != nil {
		return resp, err
	}

	newResponse := dtos.BuildAssetLinkedResponse(assetsLinked, mapsUser)

	resp.Data = newResponse
	resp.TotalRecords = count

	return resp, nil
}

func (uc *AssetLinkedUseCase) mapsUser(ctx context.Context, req []models.AssetLinkedAssetVehicleTyre) (map[string]userIdentityModels.User, error) {
	userIds := []string{}
	for _, val := range req {
		userIds = append(userIds, val.AssetLinked.CreatedBy)
		userIds = append(userIds, val.AssetLinked.UpdatedBy)
	}
	mapsUser := map[string]userIdentityModels.User{}
	users := []userIdentityModels.User{}
	err := uc.UserIdentityRepo.GetUsersByIds(ctx, uc.DB.DB(), &users, userIds)
	if err != nil {
		return mapsUser, err
	}

	for _, user := range users {
		mapsUser[user.ID] = user
	}
	return mapsUser, nil
}

func generateAssetLinkedVehicleTyreBulkUploadFilePath(clientID, name string) string {
	return clientID + "/ASSET_LINKED/VEHICLE_TYRE/BULK_UPLOAD/" + helpers.GenerateSecureFileName(name)
}

func (uc *AssetLinkedUseCase) ParseAssetLinkedVehicleTyreBulkUpload(ctx context.Context, req commonmodel.BulkUploadReq) ([]dtos.BulkUploadAssetLinkedVehicleTyreReq, []byte, error) {
	file, err := req.FileHeader.Open()
	if err != nil {
		return nil, nil, err
	}
	defer file.Close()

	bufReader := bufio.NewReader(file)
	bytes, err := io.ReadAll(bufReader)
	if err != nil {
		return nil, nil, err
	}

	bytesCopy := make([]byte, len(bytes))
	copy(bytesCopy, bytes)
	data := []dtos.BulkUploadAssetLinkedVehicleTyreReq{}
	if err := gocsv.UnmarshalBytes(bytesCopy, &data); err != nil {
		return nil, nil, err
	}

	if len(data) > 50 {
		return nil, nil, errorhandler.ErrBadRequest(errorhandler.ErrExceedMaxRow)
	}

	return data, bytes, nil
}

func (uc *AssetLinkedUseCase) ValidateAssetLinkedVehicleTyreBulkUpload(ctx context.Context, req []dtos.BulkUploadAssetLinkedVehicleTyreReq) error {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return err
	}
	clientID := claim.GetLoggedInClientID()

	assetVehicleRegisNumbers := []string{}

	serialNumbers := []string{}
	for i := range req {
		serialNumbers = append(serialNumbers, req[i].TyreSerialNumber)

		assetVehicleRegisNumbers = append(assetVehicleRegisNumbers, req[i].VehicleRegistrationNumber)
	}

	assetVehicles, err := uc.AssetVehicleRepository.GetAssetVehiclesV2(ctx, uc.DB.DB(),
		models.AssetVehicleCondition{
			Where: models.AssetVehicleWhere{
				RegistrationNumbers: assetVehicleRegisNumbers,
				ClientID:            clientID,
			},
			Preload: models.AssetVehiclePreload{},
		})
	if err != nil {
		return err
	}

	mapAssetRegistrationNumberToAssetID := map[string]string{}
	assetVehicleIDs := []string{}
	for _, assetVehicle := range assetVehicles {
		mapAssetRegistrationNumberToAssetID[assetVehicle.RegistrationNumber] = assetVehicle.AssetID
		assetVehicleIDs = append(assetVehicleIDs, assetVehicle.AssetID)
	}

	assetLinkedVehicleTyres, err := uc.AssetLinkedRepository.GetAssetLinkedTyresV2(ctx, uc.DB.DB(),
		models.AssetLinkedVehicleTyreCondition{
			Where: models.AssetLinkedVehicleTyreWhere{
				ParentAssetIDs: assetVehicleIDs,
			},
			Preload: models.AssetLinkedVehicleTyrePreload{
				AssetLinked: true,
			},
		})
	if err != nil {
		return err
	}

	mapAlreadyLinkedVehicles := map[string]bool{}
	for _, assetLinkedVehicleTyre := range assetLinkedVehicleTyres {
		mapAlreadyLinkedVehicles[assetLinkedVehicleTyre.AssetLinked.ParentAssetID] = true
	}

	// Only Get New and In Stock Tyres
	assets, err := uc.AssetRepository.GetAssets(ctx, uc.DB.DB(), models.AssetCondition{
		Where: models.AssetWhere{
			SerialNumbers: serialNumbers,
			Statuses: []string{
				constants.ASSET_STATUS_CODE_IN_STOCK,
				constants.ASSET_STATUS_CODE_NEW_STOCK,
			},
			ClientID: clientID,
		},
	})
	if err != nil {
		return err
	}

	mapAssetSerialNumberToAssetIDs := map[string]string{}
	assetTyreIDs := []string{}
	for _, asset := range assets {
		mapAssetSerialNumberToAssetIDs[asset.SerialNumber] = asset.ID
		assetTyreIDs = append(assetTyreIDs, asset.ID)
	}

	assetLinkedVehicleTyreTyres, err := uc.AssetLinkedRepository.GetAssetLinkedTyresV2(ctx, uc.DB.DB(), models.AssetLinkedVehicleTyreCondition{
		Where: models.AssetLinkedVehicleTyreWhere{
			ChildAssetIDs: assetTyreIDs,
		},
		Preload: models.AssetLinkedVehicleTyrePreload{
			AssetLinked: true,
		},
	})
	if err != nil {
		return err
	}

	mapAlreadyLinkedTyres := map[string]bool{}
	for _, assetLinkedVehicleTyre := range assetLinkedVehicleTyreTyres {
		mapAlreadyLinkedTyres[assetLinkedVehicleTyre.AssetLinked.ChildAssetID] = true
	}

	mapTyrePosition := map[string][]int{}
	for i := range req {
		req[i].IsValidToProcess = true
		req[i].ValidateVehicle(mapAssetRegistrationNumberToAssetID)
		req[i].ValidateVehicleAlreadyLinked(mapAlreadyLinkedVehicles)
		req[i].ValidateTyre(mapAssetSerialNumberToAssetIDs)
		req[i].ValidateTyreAlreadyLinked(mapAlreadyLinkedTyres)
		req[i].ValidateTyrePosition(mapTyrePosition)
	}

	return nil
}

func (uc *AssetLinkedUseCase) AssetLinkedVehicleTyreBulkUpload(ctx context.Context, fileName string, oriFileByte []byte, req []dtos.BulkUploadAssetLinkedVehicleTyreReq) (*commonmodel.CreateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	numErrorRows := 0
	for i := 0; i < len(req); i++ {
		if req[i].IsSuccess {
			continue
		}

		if !req[i].IsValidToProcess {
			numErrorRows++
			continue
		}

		resp := &CreateAssetLinkedResponse{}
		err = uc.processLinkAssetTyreWrapper(ctx, resp, &dtos.AssetLinkedResponse{
			ParentAssetID:       req[i].AssetVehicleID,
			ChildAssetID:        req[i].AssetTyreID,
			ClientID:            claim.GetLoggedInClientID(),
			LinkedAssetTypeCode: constants.ASSET_LINKED_TYPE_VEHICLE_TYRE,
			TyrePosition:        req[i].TyrePosition,
		})
		if err != nil {
			commonlogger.Warnf("failed to process bulk upload linked tyre", i, err)
			req[i].FailedReason = err.Error()
			numErrorRows++
			continue
		}

		req[i].ReferenceID = resp.AssetLinked.ID
		req[i].IsSuccess = true
	}

	uploadStatus := uploadConstants.USER_UPLOAD_STATUS_SUCCESS_CODE
	if numErrorRows > 0 {
		if numErrorRows == len(req) {
			uploadStatus = uploadConstants.USER_UPLOAD_STATUS_FAILED_CODE
		} else {
			uploadStatus = uploadConstants.USER_UPLOAD_STATUS_PARTIAL_SUCCESS_CODE
		}
	}

	resultBytes, err := gocsv.MarshalBytes(&req)
	if err != nil {
		return nil, err
	}

	oriFilePath := generateAssetLinkedVehicleTyreBulkUploadFilePath(claim.GetLoggedInClientID(), fileName)
	err = uc.storageUseCase.UploadCsvFile(ctx, oriFilePath, oriFileByte)
	if err != nil {
		return nil, err
	}

	resultFilePath := generateAssetLinkedVehicleTyreBulkUploadFilePath(claim.GetLoggedInClientID(), "result_"+fileName)
	err = uc.storageUseCase.UploadCsvFile(ctx, resultFilePath, resultBytes)
	if err != nil {
		return nil, err
	}

	userBulkUpload := &uploadModel.UserBulkUpload{
		ModelV2:            commonmodel.ModelV2{},
		BulkUploadCode:     uploadConstants.BULK_UPLOAD_ASSET_LINKED_VEHICLE_TYRE_CODE,
		StatusCode:         uploadStatus,
		OriginalFilePath:   oriFilePath,
		ResultFilePath:     resultFilePath,
		NumberOfSuccessRow: len(req) - numErrorRows,
		NumberOfFailedRow:  numErrorRows,
	}

	err = uc.uploadRepo.CreateUserBulkUploadData(ctx, uc.DB.WithCtx(ctx).DB(), userBulkUpload)
	if err != nil {
		return nil, err
	}

	resp := uploadDto.UserBulkUploadResp{
		ID:                 userBulkUpload.ID,
		BulkUploadCode:     userBulkUpload.BulkUploadCode,
		StatusCode:         userBulkUpload.StatusCode,
		OriginalFilePath:   userBulkUpload.OriginalFilePath,
		ResultFilePath:     userBulkUpload.ResultFilePath,
		NumberOfSuccessRow: userBulkUpload.NumberOfSuccessRow,
		NumberOfFailedRow:  userBulkUpload.NumberOfFailedRow,
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: userBulkUpload.ID,
		Data:        resp,
	}, nil
}

func (uc *AssetLinkedUseCase) GetAssetLinkedBonusPenaltyElig(ctx context.Context, parentAssetID string, req dtos.AssetLinkedListReq) (commonmodel.ListResponse, error) {
	resp := commonmodel.ListResponse{
		PageSize: req.PageSize,
		PageNo:   req.PageNo,
	}

	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return resp, err
	}

	count, assetsLinkedBonusPenaltyElig, err := uc.AssetLinkedRepository.GetAssetLinkedBonusPenaltyElig(
		ctx,
		uc.DB.DB(),
		models.GetAssetLinkedListParam{
			ListRequest: req.ListRequest,
			Cond: models.AssetLinkedVehicleTyreCondition{
				Where: models.AssetLinkedVehicleTyreWhere{
					ClientID:      claim.GetLoggedInClientID(),
					ParentAssetID: parentAssetID,
				},
			},
		})
	if err != nil {
		return resp, err
	}

	if len(assetsLinkedBonusPenaltyElig) == 0 {
		return resp, nil
	}

	resp.Data = assetsLinkedBonusPenaltyElig
	resp.TotalRecords = count

	return resp, nil
}

func (uc *AssetLinkedUseCase) CountTyreLinked(ctx context.Context, req dtos.CountTyreLinkedReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	linkeds, err := uc.AssetLinkedRepository.GetAssetLinkeds(ctx, uc.DB.DB(), models.AssetLinkedCondition{
		Where: models.AssetLinkedWhere{
			ClientID:      claim.GetLoggedInClientID(),
			ParentAssetID: req.ParentAssetID,
			WithUnlinked:  false,
			TypeCode:      constants.ASSET_LINKED_TYPE_VEHICLE_TYRE,
		},
		Columns: []string{"id"},
	})
	if err != nil {
		return nil, err
	}

	resp := dtos.CountTyreLinkedResp{
		NumberOfTyres: len(linkeds),
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        resp,
	}, nil
}

func (uc *AssetLinkedUseCase) calculateLinkedTyreRTDMismatch(
	ctx context.Context,
	parentAssetID string,
	tx database.DBUsecase,
) error {

	assetVehicle, err := uc.AssetVehicleRepository.GetAssetVehicle(ctx, tx.DB(), models.AssetVehicleCondition{
		Where: models.AssetVehicleWhere{
			AssetID: parentAssetID,
		},
	})
	if err != nil {
		commonlogger.Warnf("calculateLinkedTyreRTDMismatch, GetAssetVehicle err: %v", err)
		return err
	}

	if assetVehicle.MaxRtdDiffTolerance == nil {
		return nil
	}

	if !assetVehicle.MaxRtdDiffTolerance.Valid {
		return nil
	}

	if assetVehicle.AxleConfiguration.Status == pgtype.Null ||
		assetVehicle.AxleConfiguration.Status == pgtype.Undefined {
		return nil
	}

	axleConfig, err := models.AxleConfigurationJSONBToStruct(assetVehicle.AxleConfiguration)
	if err != nil {
		commonlogger.Warnf("calculateLinkedTyreRTDMismatch, err: %v", err)
		return err
	}

	if len(axleConfig) == 0 {
		return nil
	}

	linkedTyres, err := uc.AssetLinkedRepository.GetAssetLinkedTyresV2(ctx, tx.DB(), models.AssetLinkedVehicleTyreCondition{
		Where: models.AssetLinkedVehicleTyreWhere{
			ParentAssetID: parentAssetID,
		},
		Preload: models.AssetLinkedVehicleTyrePreload{
			AssetLinkedChildAsset: true,
		},
	})
	if err != nil {
		commonlogger.Warnf("calculateLinkedTyreRTDMismatch, err: %v", err)
		return err
	}

	mapTyrePairByPosition := models.MapTyrePairPositionByAxle(axleConfig)

	mapTyreLinkedByPos := map[int]models.AssetLinkedAssetVehicleTyre{}
	for _, linkedTyre := range linkedTyres {
		mapTyreLinkedByPos[int(linkedTyre.TyrePosition)] = linkedTyre
	}

	for i, j := range mapTyrePairByPosition {
		iAssetLinkedTyre, ok := mapTyreLinkedByPos[i]
		if !ok {
			// case pos i not linked
			continue
		}

		jAssetLinkedyre, ok := mapTyreLinkedByPos[j]
		if !ok {
			continue
		}

		rtdDiff := tyrecalculationutils.CalculateRTDDiff(
			iAssetLinkedTyre.AssetLinked.ChildAsset.AverageRTD,
			jAssetLinkedyre.AssetLinked.ChildAsset.AverageRTD,
			iAssetLinkedTyre.AssetLinked.ChildAsset.StartThreadDepth,
			jAssetLinkedyre.AssetLinked.ChildAsset.StartThreadDepth)

		isMismatch := null.BoolFrom(false)
		if rtdDiff > float64(assetVehicle.MaxRtdDiffTolerance.ValueOrZero()) {
			isMismatch = null.BoolFrom(true)
		}

		err := uc.AssetLinkedRepository.UpdateAssetLinkedAssetVehicleTyreV2(ctx, tx.DB(),
			&models.AssetLinkedAssetVehicleTyre{
				AssetLinkedID: iAssetLinkedTyre.AssetLinkedID,
				IsMismatch:    isMismatch,
			})
		if err != nil {
			commonlogger.Warnf("calculateLinkedTyreRTDMismatch, err: %v", err)
			return err
		}

		err = uc.AssetLinkedRepository.UpdateAssetLinkedAssetVehicleTyreV2(ctx, tx.DB(),
			&models.AssetLinkedAssetVehicleTyre{
				AssetLinkedID: jAssetLinkedyre.AssetLinkedID,
				IsMismatch:    isMismatch,
			})
		if err != nil {
			commonlogger.Warnf("calculateLinkedTyreRTDMismatch, err: %v", err)
			return err
		}

	}

	return nil
}
